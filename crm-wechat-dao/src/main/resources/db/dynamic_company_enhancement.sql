-- 企业微信动态配置增强SQL脚本
-- 作者: hongdong.xie
-- 日期: 2025-01-13 10:00:00

-- 1. 扩展cm_wechat_company表，添加动态配置支持字段
ALTER TABLE `cm_wechat_company` 
ADD COLUMN `callback_url_suffix` varchar(64) DEFAULT NULL COMMENT '回调URL后缀，如wealth、fund等' AFTER `encoding_aes_key`,
ADD COLUMN `default_app_code` varchar(64) DEFAULT NULL COMMENT '默认应用编码，用于消息发送' AFTER `callback_url_suffix`,
ADD COLUMN `sort_order` int(11) DEFAULT 0 COMMENT '排序字段' AFTER `default_app_code`,
ADD COLUMN `enabled` tinyint(4) DEFAULT 1 COMMENT '是否启用：1-启用 0-停用' AFTER `sort_order`;

-- 2. 为现有数据补充默认值
UPDATE `cm_wechat_company` SET 
  `callback_url_suffix` = CASE 
    WHEN `company_no` = '1' THEN 'wealth'
    WHEN `company_no` = '2' THEN 'fund' 
    WHEN `company_no` = '3' THEN 'hxm'
    ELSE CONCAT('company', `company_no`)
  END,
  `default_app_code` = CASE 
    WHEN `company_no` = '1' THEN 'wealth_crm'
    WHEN `company_no` = '2' THEN 'fund_customer'
    WHEN `company_no` = '3' THEN 'hxm_customer'
    ELSE NULL
  END,
  `sort_order` = CAST(`company_no` AS SIGNED),
  `enabled` = 1
WHERE `rec_stat` = '1';

-- 3. 添加唯一索引确保callback_url_suffix唯一
ALTER TABLE `cm_wechat_company` 
ADD UNIQUE KEY `uk_callback_url_suffix` (`callback_url_suffix`);

-- 4. 创建企业配置变更日志表
CREATE TABLE `cm_wechat_company_change_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `company_no` varchar(32) NOT NULL COMMENT '企业编码',
  `change_type` varchar(32) NOT NULL COMMENT '变更类型：CREATE/UPDATE/DELETE/ENABLE/DISABLE',
  `old_config` text COMMENT '变更前配置JSON',
  `new_config` text COMMENT '变更后配置JSON',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `change_reason` varchar(256) DEFAULT NULL COMMENT '变更原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_company_no` (`company_no`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业配置变更日志表';

-- 5. 示例：添加新企业配置的SQL模板
/*
INSERT INTO `cm_wechat_company` (
  `company_no`, `company_desc`, `corp_type`, `corp_id`, 
  `token`, `encoding_aes_key`, `callback_url_suffix`, 
  `default_app_code`, `sort_order`, `enabled`, 
  `creator`, `create_time`, `rec_stat`
) VALUES (
  '4', '新企业名称', '1', 'ww新企业corpid',
  '新企业token', '新企业encodingaeskey', 'newcompany',
  'newcompany_customer', 4, 1,
  'admin', NOW(), '1'
);
*/
