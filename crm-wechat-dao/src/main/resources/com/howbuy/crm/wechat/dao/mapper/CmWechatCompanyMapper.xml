<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatCompanyMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    <!--@Table cm_wechat_company-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_no" jdbcType="VARCHAR" property="companyNo" />
    <result column="company_desc" jdbcType="VARCHAR" property="companyDesc" />
    <result column="corp_type" jdbcType="VARCHAR" property="corpType" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="encoding_aes_key" jdbcType="VARCHAR" property="encodingAesKey" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
    <result column="callback_url_suffix" jdbcType="VARCHAR" property="callbackUrlSuffix" />
    <result column="default_app_code" jdbcType="VARCHAR" property="defaultAppCode" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_no, company_desc, corp_type, corp_id, token, encoding_aes_key, creator,
    create_time, modifier, modify_time, rec_stat
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from cm_wechat_company
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into cm_wechat_company (company_no, company_desc, corp_type,
    corp_id, token, encoding_aes_key,
    creator, create_time, modifier,
    modify_time, rec_stat)
    values (#{companyNo,jdbcType=VARCHAR}, #{companyDesc,jdbcType=VARCHAR}, #{corpType,jdbcType=VARCHAR},
    #{corpId,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, #{encodingAesKey,jdbcType=VARCHAR},
    #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR},
    #{modifyTime,jdbcType=TIMESTAMP}, #{recStat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into cm_wechat_company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        company_no,
      </if>
      <if test="companyDesc != null">
        company_desc,
      </if>
      <if test="corpType != null">
        corp_type,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="encodingAesKey != null">
        encoding_aes_key,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyDesc != null">
        #{companyDesc,jdbcType=VARCHAR},
      </if>
      <if test="corpType != null">
        #{corpType,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="encodingAesKey != null">
        #{encodingAesKey,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    update cm_wechat_company
    <set>
      <if test="companyNo != null">
        company_no = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyDesc != null">
        company_desc = #{companyDesc,jdbcType=VARCHAR},
      </if>
      <if test="corpType != null">
        corp_type = #{corpType,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="encodingAesKey != null">
        encoding_aes_key = #{encodingAesKey,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    update cm_wechat_company
    set company_no = #{companyNo,jdbcType=VARCHAR},
    company_desc = #{companyDesc,jdbcType=VARCHAR},
    corp_type = #{corpType,jdbcType=VARCHAR},
    corp_id = #{corpId,jdbcType=VARCHAR},
    token = #{token,jdbcType=VARCHAR},
    encoding_aes_key = #{encodingAesKey,jdbcType=VARCHAR},
    creator = #{creator,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    modifier = #{modifier,jdbcType=VARCHAR},
    modify_time = #{modifyTime,jdbcType=TIMESTAMP},
    rec_stat = #{recStat,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectConfigByCorpId" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where CORP_ID = #{corpId,jdbcType=VARCHAR}
    AND REC_STAT = '1'
  </select>

  <select id="selectConfigByCompanyNo" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
    AND REC_STAT = '1'
  </select>

  <select id="selectConfigList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where REC_STAT = '1'
  </select>
</mapper>