package com.howbuy.crm.wechat.client.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 动态企业配置信息DTO
 * 
 * <AUTHOR>
 * @date 2025-01-13 10:00:00
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class DynamicCompanyInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业编码
     */
    private String companyNo;

    /**
     * 企业描述
     */
    private String companyDesc;

    /**
     * 企业类型
     */
    private String corpType;

    /**
     * 企业微信corpId
     */
    private String corpId;

    /**
     * 回调token
     */
    private String token;

    /**
     * 回调加密key
     */
    private String encodingAesKey;

    /**
     * 回调URL后缀
     */
    private String callbackUrlSuffix;

    /**
     * 默认应用编码
     */
    private String defaultAppCode;

    /**
     * 排序字段
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 检查企业是否有效
     */
    public boolean isValid() {
        return enabled != null && enabled && 
               companyNo != null && !companyNo.trim().isEmpty() &&
               corpId != null && !corpId.trim().isEmpty();
    }

    /**
     * 获取回调URL路径
     */
    public String getCallbackUrlPath() {
        if (callbackUrlSuffix == null || callbackUrlSuffix.trim().isEmpty()) {
            return "/wechat/companyverify?companyNo=" + companyNo;
        }
        return "/wechat/" + callbackUrlSuffix + "verify";
    }
}
