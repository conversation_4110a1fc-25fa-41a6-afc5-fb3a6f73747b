/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatcustinfo;

import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import com.howbuy.crm.wechat.client.domain.request.wechatcustinfo.QueryWechatAddRelationRequest;
import com.howbuy.crm.wechat.client.domain.request.wechatcustinfo.QueryWechatCustInfoRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustAddInfoVO;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.dao.po.CmWechatRelationPO;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 微信客户信息服务
 * @date 2024/9/6 11:28
 * @since JDK 1.8
 */
@Service
@Slf4j
public class WechatCustInfoService {

    @Resource
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;

    /**
     * @param request
     * @return com.howbuy.crm.wechat.client.producer.wechatcustinfo.vo.WechatCustInfoVO
     * @description: 查询微信客户信息
     * <AUTHOR>
     * @date 2024/9/6 11:35
     * @since JDK 1.8
     */
    public WechatCustInfoVO queryWechatCustInfo(QueryWechatCustInfoRequest request) {
        if (StringUtils.isEmpty(request.getExternalUserId()) && StringUtils.isEmpty(request.getHboneNo())) {
            throw new BusinessException(ResponseCodeEnum.PARAM_ERROR);
        }
        CompanyNoEnum companyNoEnum=CompanyNoEnum.getEnum(request.getCompanyNo());
        if (companyNoEnum == null) {
            //入口 companyNo 默认赋值：
            companyNoEnum=CompanyNoEnum.HOWBUY_WEALTH;
        }

        CmWechatCustInfoPO cmWechatCustInfoPo;
        if (StringUtils.isNotEmpty(request.getExternalUserId())) {
            // 根据外部联系人id、好买财富COMPANY_NO查询微信客户信息
            cmWechatCustInfoPo = cmWechatCustInfoRepository.getWechatCustByExternalUserId(request.getExternalUserId(), companyNoEnum);
        } else {
            // 根据一账通号、好买财富COMPANY_NO获取微信客户信息
            // 优先根据国内一账通号、好买财富COMPANY_NO获取微信客户信息
            cmWechatCustInfoPo = cmWechatCustInfoRepository.getExternalUserByHboneNo(request.getHboneNo(), companyNoEnum);
            if(cmWechatCustInfoPo==null){
                // 国内一账通查不到,就根据香港一账通号、好买财富COMPANY_NO获取微信客户信息
                cmWechatCustInfoPo = cmWechatCustInfoRepository.getExternalUserByHkHboneNo(request.getHboneNo(), companyNoEnum);
            }
        }

        if (Objects.isNull(cmWechatCustInfoPo)) {
            return null;
        }

        WechatCustInfoVO wechatCustInfoVO = new WechatCustInfoVO();
        BeanUtils.copyProperties(cmWechatCustInfoPo, wechatCustInfoVO);
        return wechatCustInfoVO;
    }


    /**
     * @description:(获取request中的 companyNo . 默认值 ：好买财富)
     * @param request
     * @return com.howbuy.crm.wechat.client.enums.CompanyNoEnum
     * @author: haoran.zhang
     * @date: 2025/8/12 13:54
     * @since JDK 1.8
     */
    private static CompanyNoEnum getCompanyByRequest(BaseCompanyNoRequest request){
        CompanyNoEnum companyNoEnum=CompanyNoEnum.getEnum(request.getCompanyNo());
        if (companyNoEnum == null) {
            //入口 companyNo 默认赋值：
            companyNoEnum=CompanyNoEnum.HOWBUY_WEALTH;
        }
        return companyNoEnum;
    }

    public List<WechatCustInfoVO> queryListWechatCustInfo(QueryWechatCustInfoRequest request) {
        if (StringUtils.isEmpty(request.getExternalUserId()) && StringUtils.isEmpty(request.getHboneNo())) {
            throw new BusinessException(ResponseCodeEnum.PARAM_ERROR);
        }
        CompanyNoEnum companyNoEnum=getCompanyByRequest(request);

        List<CmWechatCustInfoPO> cmWechatCustInfoPOList = new ArrayList<>();
        // 优先根据国内一账通号、好买财富COMPANY_NO获取微信客户信息
        CmWechatCustInfoPO gncmWechatCustInfoPo = cmWechatCustInfoRepository.getExternalUserByHboneNo(request.getHboneNo(), companyNoEnum);
        cmWechatCustInfoPOList.add(gncmWechatCustInfoPo);
        // 国内一账通查不到,就根据香港一账通号、好买财富COMPANY_NO获取微信客户信息
        CmWechatCustInfoPO gwcmWechatCustInfoPo = cmWechatCustInfoRepository.getExternalUserByHkHboneNo(request.getHboneNo(), companyNoEnum);
        cmWechatCustInfoPOList.add(gwcmWechatCustInfoPo);

        List<WechatCustInfoVO> wechatCustInfoVOList = new ArrayList<>();
        cmWechatCustInfoPOList.forEach(cmWechatCustInfoPo -> {
            WechatCustInfoVO wechatCustInfoVO = new WechatCustInfoVO();
            BeanUtils.copyProperties(cmWechatCustInfoPo, wechatCustInfoVO);
            wechatCustInfoVOList.add(wechatCustInfoVO);
        });
        return wechatCustInfoVOList;
    }


    /**
     * @description:(请在此添加描述)
     * @param request
     * @return com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustAddInfoVO
     * @author: xufanchao
     * @date: 2025/6/10 18:02
     * @since JDK 1.8
     */
    public WechatCustAddInfoVO queryWechatAddRelationList(QueryWechatAddRelationRequest request) {
        WechatCustAddInfoVO wechatCustAddInfoVO = new WechatCustAddInfoVO();
        if (StringUtils.isEmpty(request.getHboneNo())) {
            throw new BusinessException(ResponseCodeEnum.PARAM_ERROR);
        }
        CompanyNoEnum companyNoEnum=getCompanyByRequest(request);

        List<CmWechatRelationPO> cmWechatRelationPOS = cmWechatCustInfoRepository.queryWechatAddRelationList(companyNoEnum.getCode(),
                request.getHboneNo(), request.getConsCodeList());
        List<WechatCustAddInfoVO.RealtionVO> realtionVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cmWechatRelationPOS)) {
            cmWechatRelationPOS.forEach(cmWechatRelationPO -> {
                WechatCustAddInfoVO.RealtionVO realtionVO = new WechatCustAddInfoVO.RealtionVO();
                realtionVO.setConsCode(cmWechatRelationPO.getConsCode());
                realtionVO.setAddTime(cmWechatRelationPO.getAddTime());
                realtionVO.setCompanyNo(cmWechatRelationPO.getCompanyNo());
                realtionVOList.add(realtionVO);
            });
        }
        wechatCustAddInfoVO.setRealtionVOList(realtionVOList);
        return wechatCustAddInfoVO;
    }
}
