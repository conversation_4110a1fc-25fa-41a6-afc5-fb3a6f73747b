/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: (企业微信-企业主体-映射关系)
 * <AUTHOR>
 * @date 2025/7/11 13:12
 * @since JDK 1.8
 */
@Slf4j
public class WechatCorpUtil {



    /**
     * 根据公司编号获取发送消息的微信应用枚举
     * @param companyNoEnum 企微-企业主体
     * @return
     */
    public static WechatApplicationEnum getSendMsgAppEnum(CompanyNoEnum companyNoEnum){
        switch (companyNoEnum){
            case HOWBUY_WEALTH:
                return WechatApplicationEnum.WEALTH_CRM;
                case HOWBUY_FUND:
                //Crocodile's TODO : 企业默认发送消息的内建应用，如需要，请自行添加
                return null;
                default:
                return null;
        }
    }



}