/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: (企微基础配置service)
 * <AUTHOR>
 * @date 2025/7/24 10:01
 * @since JDK 1.8
 */
@Slf4j
@Service
public class BaseConfigServce {
    @Autowired
    private  CacheBaseConfigServce cacheBaseConfigServce;

    /**
     * 企微 应用 类型
     *企业微信-内建应用- 客户联系
     */
    private static final String  APPLICATION_TYPE_CUSTOMER= "customer";


    /**
     * 获取企业微信corpId
     * @param companyNoEnum  企微-企业主体
     * @return
     */
    public String  getCorpId(CompanyNoEnum companyNoEnum){
        CorpUtilityDTO utilityDTO= getCorpUtilityDTO(companyNoEnum);
        if(utilityDTO==null){
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO.getCorpId();
    }



    /**
     * 获取企业微信corpId
     * @param corpId
     * @return
     */
    public CompanyNoEnum getCompanyNoEnum(String corpId){
        CompanyNoEnum companyNoEnum=null;
        Map<String,CorpUtilityDTO>  corpConfigMap= cacheBaseConfigServce.getCorpConfigMap() ;
        for (Map.Entry<String, CorpUtilityDTO> entry : corpConfigMap.entrySet()) {
            String companyNo = entry.getKey();
            CorpUtilityDTO utilityDTO = entry.getValue();
            if (utilityDTO.getCorpId().equals(corpId)) {
                companyNoEnum = CompanyNoEnum.getEnum(companyNo);
            }
        }
        return companyNoEnum;

//        CmWechatCompanyPO companyInfo= corpConfigRepository.getCorpInfoByCorpId(corpId);
//        return companyInfo==null?null:CompanyNoEnum.getEnum(companyInfo.getCompanyNo());
    }

    /**
     * @description: 获取企业微信工具类对象
     * @param companyNoEnum
     * @return com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public CorpUtilityDTO getCorpUtilityDTO(CompanyNoEnum companyNoEnum) {
//        CmWechatCompanyPO companyPO= getCorpInfo(companyNoEnum);
//        if(companyPO==null){
//            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//        return new CorpUtilityDTO(companyNoEnum.getCode(),companyPO.getCorpId(), companyPO.getToken(), companyPO.getEncodingAesKey());
        //从cache中获取
        CorpUtilityDTO  utilityDTO=cacheBaseConfigServce.getCorpConfig(companyNoEnum.getCode());
        if(utilityDTO==null){
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO;

    }


    /**
     * @description:构建WXBizMsgCrypt对象
     * @param companyNoEnum
     * @return com.howbuy.crm.wechat.service.commom.utils.WXBizMsgCrypt
     * @author: yu.zhang
     * @date: 2023/6/8 15:09
     * @since JDK 1.8
     */
    public  WXBizMsgCrypt buildWXBizMsgCrypt(CompanyNoEnum companyNoEnum) {
        WXBizMsgCrypt wxcpt = null;
        try {
            CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNoEnum);
            wxcpt = new WXBizMsgCrypt(corpUtilityDTO.getToken(), corpUtilityDTO.getEncodingAesKey(), corpUtilityDTO.getCorpId());
        } catch (Exception e) {
            log.error("buildWXBizMsgCrypt Exception:{}", e.getMessage(),e);
        }
        return wxcpt;
    }



    /**
     * @description: 获取应用工具类对象
     * @param applicationEnum
     * @return com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public ApplicationUtilityDTO getApplicationUtilityDTO(WechatApplicationEnum applicationEnum) {

        //从cache中获取
        ApplicationUtilityDTO utilityDTO=cacheBaseConfigServce.getApplicationConfig(applicationEnum.getCode());
        if(utilityDTO==null){
            log.info("企微应用：{} 配置不存在！",applicationEnum);
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO;



//        CmWechatApplicationPO applicationInfo=corpConfigRepository.getApplicationInfo(applicationEnum.getCode());
//        if(applicationInfo==null){
//            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//        //归属 公司
//        String companyNo=applicationInfo.getCompanyNo();
//        CmWechatCompanyPO   corpInfo= corpConfigRepository.getCorpInfoByCompanyNo(companyNo);
//        if (corpInfo==null){
//            log.info("企微应用：{} 对应companyNo:{},配置不存在！",applicationEnum,companyNo);
//            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//
//
//        ApplicationUtilityDTO utilityDTO = new ApplicationUtilityDTO();
//        //应用信息
//        utilityDTO.setApplicationCode(applicationEnum.getCode());
//        utilityDTO.setAgentId(applicationInfo.getAgentId());
//        utilityDTO.setAccessSecret(applicationInfo.getAccessSecret());
//
//        //补充该公司信息
//        utilityDTO.setCorpId(corpInfo.getCorpId());
//        utilityDTO.setToken(corpInfo.getToken());
//        utilityDTO.setEncodingAesKey(corpInfo.getEncodingAesKey());
//
//        return utilityDTO;

    }


    /**
     * @description: 根据公司编号获取 [企业微信-客户联系]  应用枚举
     * @param companyNoEnum
     * @return com.howbuy.crm.wechat.service.commom.enums.WechatApplicationEnum
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public WechatApplicationEnum getCustApplicationEnum(CompanyNoEnum companyNoEnum) {

        Map<String,ApplicationUtilityDTO>  cacheMap=cacheBaseConfigServce.getApplicationConfigMap();
        List<ApplicationUtilityDTO> custAppList =
        cacheMap.values().stream()
                //过滤： 公司
                .filter(applicationUtilityDTO -> companyNoEnum.getCode().equals(applicationUtilityDTO.getCompanyNo()))
                //过滤： 应用类型 ： 客户联系
                .filter(applicationUtilityDTO -> APPLICATION_TYPE_CUSTOMER.equals(applicationUtilityDTO.getApplicationType()))
                .collect(Collectors.toList());

        if(custAppList.size()==1){
            return WechatApplicationEnum.getEnum(custAppList.get(0).getApplicationCode());
        }else {
            log.error("companyNo:{},[内建应用-客户联系]配置信息：{}，配置信息有误！",companyNoEnum, JSON.toJSONString(custAppList));
        }
       return null;
    }

}