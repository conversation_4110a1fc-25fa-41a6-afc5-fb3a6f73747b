package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.crm.wechat.client.domain.dto.DynamicCompanyInfoDTO;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.service.service.config.DynamicCompanyConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 企业配置兼容性工具类
 * 用于在枚举和动态配置之间提供兼容性支持
 * 
 * <AUTHOR>
 * @date 2025-01-13 10:00:00
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CompanyConfigCompatibilityUtil {

    @Autowired
    private DynamicCompanyConfigService dynamicCompanyConfigService;

    /**
     * 智能获取企业编码
     * 优先使用传入的companyNo，如果为空或无效则使用默认值
     * 
     * @param companyNo 企业编码
     * @return 有效的企业编码
     */
    public String getValidCompanyNo(String companyNo) {
        // 如果传入的companyNo有效，直接使用
        if (!StringUtils.isEmpty(companyNo) && dynamicCompanyConfigService.isValidCompany(companyNo)) {
            return companyNo;
        }
        
        // 如果传入的companyNo无效，记录警告并使用默认值
        if (!StringUtils.isEmpty(companyNo)) {
            log.warn("传入的企业编码无效: {}, 使用默认企业编码: {}", 
                companyNo, dynamicCompanyConfigService.getDefaultCompanyNo());
        }
        
        return dynamicCompanyConfigService.getDefaultCompanyNo();
    }

    /**
     * 从CompanyNoEnum获取企业编码（兼容性方法）
     * 
     * @param companyNoEnum 企业枚举
     * @return 企业编码
     */
    public String getCompanyNoFromEnum(CompanyNoEnum companyNoEnum) {
        if (companyNoEnum == null) {
            return dynamicCompanyConfigService.getDefaultCompanyNo();
        }
        return companyNoEnum.getCode();
    }

    /**
     * 获取企业配置信息
     * 
     * @param companyNo 企业编码
     * @return 企业配置信息
     */
    public DynamicCompanyInfoDTO getCompanyInfo(String companyNo) {
        String validCompanyNo = getValidCompanyNo(companyNo);
        return dynamicCompanyConfigService.getCompanyInfo(validCompanyNo);
    }

    /**
     * 获取企业配置信息（从枚举）
     * 
     * @param companyNoEnum 企业枚举
     * @return 企业配置信息
     */
    public DynamicCompanyInfoDTO getCompanyInfo(CompanyNoEnum companyNoEnum) {
        String companyNo = getCompanyNoFromEnum(companyNoEnum);
        return dynamicCompanyConfigService.getCompanyInfo(companyNo);
    }

    /**
     * 检查企业是否有效
     * 
     * @param companyNo 企业编码
     * @return 是否有效
     */
    public boolean isValidCompany(String companyNo) {
        return dynamicCompanyConfigService.isValidCompany(companyNo);
    }

    /**
     * 根据回调URL后缀获取企业编码
     * 
     * @param callbackSuffix 回调URL后缀
     * @return 企业编码
     */
    public String getCompanyNoByCallbackSuffix(String callbackSuffix) {
        String companyNo = dynamicCompanyConfigService.getCompanyNoByCallbackSuffix(callbackSuffix);
        if (StringUtils.isEmpty(companyNo)) {
            log.warn("未找到回调后缀对应的企业: {}, 使用默认企业", callbackSuffix);
            return dynamicCompanyConfigService.getDefaultCompanyNo();
        }
        return companyNo;
    }

    /**
     * 获取企业的CorpId
     * 
     * @param companyNo 企业编码
     * @return CorpId
     */
    public String getCorpId(String companyNo) {
        DynamicCompanyInfoDTO companyInfo = getCompanyInfo(companyNo);
        return companyInfo != null ? companyInfo.getCorpId() : null;
    }

    /**
     * 获取企业的回调Token
     * 
     * @param companyNo 企业编码
     * @return Token
     */
    public String getToken(String companyNo) {
        DynamicCompanyInfoDTO companyInfo = getCompanyInfo(companyNo);
        return companyInfo != null ? companyInfo.getToken() : null;
    }

    /**
     * 获取企业的回调加密Key
     * 
     * @param companyNo 企业编码
     * @return EncodingAESKey
     */
    public String getEncodingAesKey(String companyNo) {
        DynamicCompanyInfoDTO companyInfo = getCompanyInfo(companyNo);
        return companyInfo != null ? companyInfo.getEncodingAesKey() : null;
    }

    /**
     * 获取企业的默认应用编码
     * 
     * @param companyNo 企业编码
     * @return 默认应用编码
     */
    public String getDefaultAppCode(String companyNo) {
        DynamicCompanyInfoDTO companyInfo = getCompanyInfo(companyNo);
        return companyInfo != null ? companyInfo.getDefaultAppCode() : null;
    }
}
