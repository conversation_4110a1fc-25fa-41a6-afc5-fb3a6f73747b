package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.client.domain.dto.DynamicCompanyInfoDTO;
import com.howbuy.crm.wechat.service.service.config.DynamicCompanyConfigService;
import com.howbuy.crm.wechat.service.commom.dto.ReturnMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态企业配置管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-13 10:00:00
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/dynamic-company-config")
public class DynamicCompanyConfigController {

    @Autowired
    private DynamicCompanyConfigService dynamicCompanyConfigService;

    /**
     * 获取所有有效企业配置
     * 
     * @return 企业配置列表
     */
    @GetMapping("/list")
    public ReturnMessageDto<List<DynamicCompanyInfoDTO>> getAllValidCompanies() {
        try {
            List<DynamicCompanyInfoDTO> companies = dynamicCompanyConfigService.getAllValidCompanies();
            return ReturnMessageDto.ok(companies);
        } catch (Exception e) {
            log.error("获取企业配置列表失败", e);
            return ReturnMessageDto.fail("获取企业配置列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业编码获取企业配置
     * 
     * @param companyNo 企业编码
     * @return 企业配置信息
     */
    @GetMapping("/get/{companyNo}")
    public ReturnMessageDto<DynamicCompanyInfoDTO> getCompanyInfo(@PathVariable String companyNo) {
        try {
            DynamicCompanyInfoDTO companyInfo = dynamicCompanyConfigService.getCompanyInfo(companyNo);
            if (companyInfo == null) {
                return ReturnMessageDto.fail("企业配置不存在: " + companyNo);
            }
            return ReturnMessageDto.ok(companyInfo);
        } catch (Exception e) {
            log.error("获取企业配置失败: companyNo={}", companyNo, e);
            return ReturnMessageDto.fail("获取企业配置失败: " + e.getMessage());
        }
    }

    /**
     * 检查企业是否有效
     * 
     * @param companyNo 企业编码
     * @return 是否有效
     */
    @GetMapping("/validate/{companyNo}")
    public ReturnMessageDto<Boolean> validateCompany(@PathVariable String companyNo) {
        try {
            boolean isValid = dynamicCompanyConfigService.isValidCompany(companyNo);
            return ReturnMessageDto.ok(isValid);
        } catch (Exception e) {
            log.error("验证企业配置失败: companyNo={}", companyNo, e);
            return ReturnMessageDto.fail("验证企业配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据回调URL后缀获取企业编码
     * 
     * @param callbackSuffix 回调URL后缀
     * @return 企业编码
     */
    @GetMapping("/callback-mapping/{callbackSuffix}")
    public ReturnMessageDto<String> getCompanyNoByCallbackSuffix(@PathVariable String callbackSuffix) {
        try {
            String companyNo = dynamicCompanyConfigService.getCompanyNoByCallbackSuffix(callbackSuffix);
            if (companyNo == null) {
                return ReturnMessageDto.fail("回调后缀不存在: " + callbackSuffix);
            }
            return ReturnMessageDto.ok(companyNo);
        } catch (Exception e) {
            log.error("获取回调映射失败: callbackSuffix={}", callbackSuffix, e);
            return ReturnMessageDto.fail("获取回调映射失败: " + e.getMessage());
        }
    }

    /**
     * 重新加载所有企业配置
     * 
     * @return 操作结果
     */
    @PostMapping("/reload")
    public ReturnMessageDto<String> reloadAllCompanyConfig() {
        try {
            dynamicCompanyConfigService.reloadAllCompanyConfig();
            return ReturnMessageDto.ok("企业配置重新加载成功");
        } catch (Exception e) {
            log.error("重新加载企业配置失败", e);
            return ReturnMessageDto.fail("重新加载企业配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取企业配置统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    public ReturnMessageDto<Object> getConfigStats() {
        try {
            List<DynamicCompanyInfoDTO> companies = dynamicCompanyConfigService.getAllValidCompanies();
            
            return ReturnMessageDto.ok(new Object() {
                public final int totalCompanies = companies.size();
                public final List<String> companyNos = companies.stream()
                    .map(DynamicCompanyInfoDTO::getCompanyNo)
                    .collect(java.util.stream.Collectors.toList());
                public final List<String> callbackSuffixes = companies.stream()
                    .map(DynamicCompanyInfoDTO::getCallbackUrlSuffix)
                    .filter(suffix -> suffix != null && !suffix.trim().isEmpty())
                    .collect(java.util.stream.Collectors.toList());
            });
        } catch (Exception e) {
            log.error("获取配置统计信息失败", e);
            return ReturnMessageDto.fail("获取配置统计信息失败: " + e.getMessage());
        }
    }
}
