package com.howbuy.crm.wechat.service.service.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.client.domain.dto.DynamicCompanyInfoDTO;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO;
import com.howbuy.crm.wechat.service.commom.cache.AbstractCacheService;
import com.howbuy.crm.wechat.service.repository.CmWechatCorpConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 动态企业配置服务
 * 
 * <AUTHOR>
 * @date 2025-01-13 10:00:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class DynamicCompanyConfigService extends AbstractCacheService implements InitializingBean {

    @Autowired
    private CmWechatCorpConfigRepository corpConfigRepository;

    /**
     * 动态企业配置缓存key
     */
    private static final String DYNAMIC_COMPANY_CACHE_KEY = "DYNAMIC_COMPANY_CONFIG";

    /**
     * 回调URL后缀到企业编码的映射缓存
     */
    private static final String CALLBACK_SUFFIX_MAPPING_CACHE_KEY = "CALLBACK_SUFFIX_MAPPING";

    @Override
    public void afterPropertiesSet() throws Exception {
        reloadAllCompanyConfig();
    }

    /**
     * 重新加载所有企业配置
     */
    public void reloadAllCompanyConfig() {
        List<CmWechatCompanyPO> companyList = corpConfigRepository.selectCorpConfigList();
        Map<String, DynamicCompanyInfoDTO> companyMap = Maps.newConcurrentMap();
        Map<String, String> callbackSuffixMap = Maps.newConcurrentMap();

        for (CmWechatCompanyPO companyPO : companyList) {
            DynamicCompanyInfoDTO companyInfo = convertToDTO(companyPO);
            if (companyInfo.isValid()) {
                companyMap.put(companyInfo.getCompanyNo(), companyInfo);
                
                // 建立回调URL后缀映射
                if (!StringUtils.isEmpty(companyInfo.getCallbackUrlSuffix())) {
                    callbackSuffixMap.put(companyInfo.getCallbackUrlSuffix(), companyInfo.getCompanyNo());
                }
                
                log.info("动态企业配置加载: companyNo={}, companyDesc={}, callbackSuffix={}", 
                    companyInfo.getCompanyNo(), companyInfo.getCompanyDesc(), companyInfo.getCallbackUrlSuffix());
            }
        }

        // 更新缓存
        CACHE_SERVICE.putObjToMap(DYNAMIC_COMPANY_CACHE_KEY, "ALL", companyMap);
        CACHE_SERVICE.putObjToMap(CALLBACK_SUFFIX_MAPPING_CACHE_KEY, "ALL", callbackSuffixMap);
        
        log.info("动态企业配置重新加载完成，共加载{}个有效企业", companyMap.size());
    }

    /**
     * 根据企业编码获取企业配置
     */
    public DynamicCompanyInfoDTO getCompanyInfo(String companyNo) {
        if (StringUtils.isEmpty(companyNo)) {
            return null;
        }
        
        Map<String, DynamicCompanyInfoDTO> companyMap = getCompanyConfigMap();
        return companyMap.get(companyNo);
    }

    /**
     * 根据回调URL后缀获取企业编码
     */
    public String getCompanyNoByCallbackSuffix(String callbackSuffix) {
        if (StringUtils.isEmpty(callbackSuffix)) {
            return null;
        }
        
        Map<String, String> suffixMap = getCallbackSuffixMap();
        return suffixMap.get(callbackSuffix);
    }

    /**
     * 获取所有有效的企业配置
     */
    public List<DynamicCompanyInfoDTO> getAllValidCompanies() {
        Map<String, DynamicCompanyInfoDTO> companyMap = getCompanyConfigMap();
        return companyMap.values().stream()
                .filter(DynamicCompanyInfoDTO::isValid)
                .sorted((a, b) -> {
                    int orderA = a.getSortOrder() != null ? a.getSortOrder() : 999;
                    int orderB = b.getSortOrder() != null ? b.getSortOrder() : 999;
                    return Integer.compare(orderA, orderB);
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查企业是否存在且有效
     */
    public boolean isValidCompany(String companyNo) {
        DynamicCompanyInfoDTO companyInfo = getCompanyInfo(companyNo);
        return companyInfo != null && companyInfo.isValid();
    }

    /**
     * 兼容性方法：将CompanyNoEnum转换为企业编码
     */
    public String getCompanyNoFromEnum(CompanyNoEnum companyNoEnum) {
        return companyNoEnum != null ? companyNoEnum.getCode() : null;
    }

    /**
     * 兼容性方法：根据企业编码获取对应的枚举（如果存在）
     */
    public CompanyNoEnum getCompanyNoEnum(String companyNo) {
        return CompanyNoEnum.getEnum(companyNo);
    }

    /**
     * 获取默认企业编码（用于兼容性）
     */
    public String getDefaultCompanyNo() {
        return CompanyNoEnum.HOWBUY_WEALTH.getCode();
    }

    /**
     * 转换PO为DTO
     */
    private DynamicCompanyInfoDTO convertToDTO(CmWechatCompanyPO companyPO) {
        DynamicCompanyInfoDTO dto = new DynamicCompanyInfoDTO();
        BeanUtils.copyProperties(companyPO, dto);
        dto.setEnabled("1".equals(companyPO.getRecStat()));
        return dto;
    }

    /**
     * 获取企业配置映射
     */
    @SuppressWarnings("unchecked")
    private Map<String, DynamicCompanyInfoDTO> getCompanyConfigMap() {
        Object cacheObj = CACHE_SERVICE.getObjFromMap(DYNAMIC_COMPANY_CACHE_KEY, "ALL");
        if (cacheObj instanceof Map) {
            return (Map<String, DynamicCompanyInfoDTO>) cacheObj;
        }
        return new ConcurrentHashMap<>();
    }

    /**
     * 获取回调后缀映射
     */
    @SuppressWarnings("unchecked")
    private Map<String, String> getCallbackSuffixMap() {
        Object cacheObj = CACHE_SERVICE.getObjFromMap(CALLBACK_SUFFIX_MAPPING_CACHE_KEY, "ALL");
        if (cacheObj instanceof Map) {
            return (Map<String, String>) cacheObj;
        }
        return new ConcurrentHashMap<>();
    }
}
