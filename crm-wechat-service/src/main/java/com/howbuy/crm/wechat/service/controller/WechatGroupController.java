/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo;
import com.howbuy.crm.wechat.service.service.group.WechatGroupService;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 企微外部联系人接口类
 * <AUTHOR>
 * @date 2023/11/1 13:20
 * @since JDK 1.8
 */

@Slf4j
@RestController
@RequestMapping("/wechatgroup")
public class WechatGroupController {

    @Autowired
    private WechatGroupService wechatGroupService;

    /**
     * @api {GET} /wechatgroup/getgroupinfobyuserid getGroupInfoByUserId()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupController
     * @apiName getGroupInfoByUserId()
     * @apiDescription 根据员工账号查询所有有他的群
     * @apiParam (请求参数) {String} userId
     * @apiParamExample 请求参数示例
     * userId=EMdAcJsUBw
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccess (响应结果) {String} response.chatId 客户群ID
     * @apiSuccess (响应结果) {String} response.chatName 群名
     * @apiSuccess (响应结果) {String} response.chatOwner 群主ID
     * @apiSuccess (响应结果) {Number} response.createTime 群的创建时间
     * @apiSuccess (响应结果) {String} response.notice 群公告
     * @apiSuccess (响应结果) {Array} response.chatMemberList 群成员列表
     * @apiSuccess (响应结果) {String} response.chatMemberList.userId 群成员id
     * @apiSuccess (响应结果) {String} response.chatMemberList.type 成员类型       1 - 企业成员      2 - 外部联系人
     * @apiSuccess (响应结果) {String} response.chatMemberList.unionid 外部联系人在微信开放平台的唯一身份标识（微信unionid）
     * @apiSuccess (响应结果) {Number} response.chatMemberList.joinTime 入群时间
     * @apiSuccess (响应结果) {String} response.chatMemberList.joinScene 入群方式。      1 - 由群成员邀请入群（直接邀请入群）      2 - 由群成员邀请入群（通过邀请链接入群）      3 - 通过扫描群二维码入群
     * @apiSuccess (响应结果) {String} response.chatMemberList.invitorUserId 邀请者。目前仅当是由本企业内部成员邀请入群时会返回该值      邀请者的userid
     * @apiSuccess (响应结果) {String} response.chatMemberList.groupNickname 在群里的昵称
     * @apiSuccess (响应结果) {String} response.chatMemberList.name 如果是微信用户，则返回其在微信中设置的名字          如果是企业微信联系人，则返回其设置对外展示的别名或实名
     * @apiSuccess (响应结果) {Array} response.adminUserIdList 群管理员userid列表
     * @apiSuccessExample 响应结果示例
     * [{"chatId":"Qy8VyWQfjr","createTime":1365663433809,"adminUserIdList":["ibAbK8"],"chatName":"Rde","chatOwner":"ZKeGb8QGm7","chatMemberList":[{"invitorUserId":"8pzJy","groupNickname":"yqC","unionid":"IHA4hUc","joinTime":2652249958197,"name":"29S912M","joinScene":"7","type":"X","userId":"TK6"}],"notice":"qdhSZ"}]
     */
    @GetMapping("/getgroupinfobyuserid")
    @ResponseBody
    public List<GroupChatInfo> getGroupInfoByUserId(String userId) {
        //入口 companyNo 默认赋值：
        CompanyNoEnum companyNoEnum= CompanyNoEnum.HOWBUY_WEALTH;
        return wechatGroupService.getGroupInfoByUserId(companyNoEnum,userId);
    }

    /**
     * @api {GET} /wechatgroup/getgroupinfobychatid getGroupInfoByChatId()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupController
     * @apiName getGroupInfoByChatId()
     * @apiDescription 根据群ID获取群信息
     * @apiParam (请求参数) {String} chatId
     * @apiParam (请求参数) {String} corpId
     * @apiParam (请求参数) {String} corpSecret
     * @apiParamExample 请求参数示例
     * chatId=Hey
     * @apiSuccess (响应结果) {String} chatId 客户群ID
     * @apiSuccess (响应结果) {String} chatName 群名
     * @apiSuccess (响应结果) {String} chatOwner 群主ID
     * @apiSuccess (响应结果) {Number} createTime 群的创建时间
     * @apiSuccess (响应结果) {String} notice 群公告
     * @apiSuccess (响应结果) {Array} chatMemberList 群成员列表
     * @apiSuccess (响应结果) {String} chatMemberList.userId 群成员id
     * @apiSuccess (响应结果) {String} chatMemberList.type 成员类型       1 - 企业成员      2 - 外部联系人
     * @apiSuccess (响应结果) {String} chatMemberList.unionid 外部联系人在微信开放平台的唯一身份标识（微信unionid）
     * @apiSuccess (响应结果) {Number} chatMemberList.joinTime 入群时间
     * @apiSuccess (响应结果) {String} chatMemberList.joinScene 入群方式。      1 - 由群成员邀请入群（直接邀请入群）      2 - 由群成员邀请入群（通过邀请链接入群）      3 - 通过扫描群二维码入群
     * @apiSuccess (响应结果) {String} chatMemberList.invitorUserId 邀请者。目前仅当是由本企业内部成员邀请入群时会返回该值      邀请者的userid
     * @apiSuccess (响应结果) {String} chatMemberList.groupNickname 在群里的昵称
     * @apiSuccess (响应结果) {String} chatMemberList.name 如果是微信用户，则返回其在微信中设置的名字          如果是企业微信联系人，则返回其设置对外展示的别名或实名
     * @apiSuccess (响应结果) {Array} adminUserIdList 群管理员userid列表
     * @apiSuccessExample 响应结果示例
     * {"chatId":"xy01ujvFB","createTime":1311568597692,"adminUserIdList":["HhKZ3I00"],"chatName":"zyFAt","chatOwner":"geM5Re","chatMemberList":[{"invitorUserId":"IqiZi","groupNickname":"riWrDJtS","unionid":"3mQ1e","joinTime":945980143099,"name":"3268","joinScene":"aD7OxfGX","type":"lBPx1kmVNJ","userId":"lqnK"}],"notice":"Z28dV"}
     */
    @GetMapping("/getgroupinfobychatid")
    @ResponseBody
    public GroupChatInfo getGroupInfoByChatId(String chatId,String corpId, String corpSecret) {
        //corpId 和 corpSecret 都有值时，动态请求企微服务端 获取
        if(StringUtil.isNotNullStr(corpId) && StringUtil.isNotNullStr(corpSecret)){
            return wechatGroupService.getDynamicGroupInfoByChatId(chatId, corpId, corpSecret);
        }
        //默认： 历史逻辑， 请求crm内部服务数据
        return wechatGroupService.getGroupInfoByChatId(chatId,null);
    }

}